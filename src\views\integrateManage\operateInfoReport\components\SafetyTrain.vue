<template>
  <div class="safety-container">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="安全培训统计"></SubTitle>
      </el-col>
      <el-col :xl="12" class="time-screen">
        <el-date-picker
          v-model="year"
          type="year"
          placeholder="请选择年份"
          :clearable="false"
          @change="handleChange"
        ></el-date-picker>
      </el-col>
    </el-row>
    <main class="safety-main">
      <div class="safety-main-left">
        <div class="chart" id="safety-chart"></div>
      </div>
      <div class="safety-main-right">
        <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }" max-height="500">
          <el-table-column prop="plateNumber" label="季度" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="培训次数" align="center"></el-table-column>
          <el-table-column prop="participantCount" label="参与人数" align="center"></el-table-column>
        </el-table>
      </div>
    </main>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import moment from "moment";
  import * as echarts from "echarts";

  export default {
    components: {
      SubTitle,
    },
    data() {
      return {
        tableData: [
          { plateNumber: "第一季度", operatorName: "12", participantCount: "320" },
          { plateNumber: "第二季度", operatorName: "8", participantCount: "280" },
          { plateNumber: "第三季度", operatorName: "15", participantCount: "450" },
          { plateNumber: "第四季度", operatorName: "10", participantCount: "380" },
        ],
        year: moment().format("YYYY"),
        chart: null,
      };
    },
    mounted() {
      this.initChart();
      window.addEventListener("resize", this.resizeChart);
    },
    destroyed() {
      if (this.chart) {
        this.chart.dispose();
      }
      window.removeEventListener("resize", this.resizeChart);
    },
    methods: {
      handleChange() {
        // 年份变化时重新加载数据和图表
        this.loadData();
        this.updateChart();
      },

      loadData() {
        // 这里可以根据选择的年份加载对应的数据
        // 暂时使用模拟数据
      },

      initChart() {
        this.chart = echarts.init(document.getElementById("safety-chart"));

        const chartData = [
          { name: "第一季度", value: 12, participantCount: 320 },
          { name: "第二季度", value: 8, participantCount: 280 },
          { name: "第三季度", value: 15, participantCount: 450 },
          { name: "第四季度", value: 10, participantCount: 380 },
        ];

        const option = {
          tooltip: {
            trigger: "item",
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            borderColor: "#fff",
            borderWidth: 1,
            textStyle: {
              color: "#fff",
              fontSize: 12,
            },
            formatter: function (params) {
              const data = chartData.find((item) => item.name === params.name);
              return `${params.name}<br/>培训次数: ${data.value}<br/>参与人数: ${data.participantCount}`;
            },
          },
          legend: {
            orient: "horizontal",
            top: "5%",
            left: "center",
            itemWidth: 12,
            itemHeight: 12,
            textStyle: {
              color: "#333",
              fontSize: 12,
            },
            data: ["第一季度", "第二季度", "第三季度", "第四季度"],
          },
          series: [
            {
              name: "安全培训统计",
              type: "pie",
              radius: ["40%", "70%"],
              center: ["50%", "60%"],
              avoidLabelOverlap: false,
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              data: chartData,
            },
          ],
          color: ["#5470c6", "#91cc75", "#fac858", "#ee6666"],
        };

        this.chart.setOption(option);
      },

      updateChart() {
        if (this.chart) {
          // 这里可以根据新的年份数据更新图表
          this.initChart();
        }
      },

      resizeChart() {
        if (this.chart) {
          this.chart.resize();
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .safety-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
    margin-top: 20px;
  }
  .time-screen {
    display: flex;
    justify-content: flex-end;
  }
  .safety-main {
    display: flex;
    .safety-main-left {
      flex: 1;
      overflow: hidden;
      .chart {
        width: 100%;
        height: 500px;
      }
    }
    .safety-main-right {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
